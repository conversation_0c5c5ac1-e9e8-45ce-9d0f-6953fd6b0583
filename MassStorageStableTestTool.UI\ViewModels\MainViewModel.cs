using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MassStorageStableTestTool.UI.Models;
using System.Collections.ObjectModel;
using Microsoft.Extensions.Logging;
using LogLevel = MassStorageStableTestTool.UI.Models.LogLevel;
using MassStorageStableTestTool.UI.Resources;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Automation.Services;
using System.Windows;
using UIDriveInfo = MassStorageStableTestTool.UI.Models.DriveInfo;

namespace MassStorageStableTestTool.UI.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly ILogger<MainViewModel> _logger;
        private readonly ITestOrchestrator _testOrchestrator;
        private readonly ISystemInfoService _systemInfoService;
        private CancellationTokenSource? _testCancellationTokenSource;

        [ObservableProperty]
        private ObservableCollection<UIDriveInfo> _availableDrives = new();

        [ObservableProperty]
        private int _selectedDriveCount;

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _guiTools = new();

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _cliTools = new();

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _hybridTools = new();

        [ObservableProperty]
        private ObservableCollection<LogEntry> _logEntries = new();

        [ObservableProperty]
        private string _currentStatus = Strings.Status_WaitingStart;

        [ObservableProperty]
        private double _overallProgress = 0;

        [ObservableProperty]
        private string _currentTestName = string.Empty;

        [ObservableProperty]
        private double _currentTestProgress = 0;

        [ObservableProperty]
        private string _estimatedTimeRemaining = string.Empty;

        [ObservableProperty]
        private bool _isTestRunning = false;

        [ObservableProperty]
        private string _statusBarText = Strings.Status_Ready;

        [ObservableProperty]
        private double _cpuUsage = 0;

        [ObservableProperty]
        private double _memoryUsage = 0;

        public MainViewModel(
            ILogger<MainViewModel> logger,
            ITestOrchestrator testOrchestrator,
            ISystemInfoService systemInfoService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _testOrchestrator = testOrchestrator ?? throw new ArgumentNullException(nameof(testOrchestrator));
            _systemInfoService = systemInfoService ?? throw new ArgumentNullException(nameof(systemInfoService));

            // 订阅测试编排器事件
            _testOrchestrator.StatusChanged += OnTestSuiteStatusChanged;
            _testOrchestrator.ToolStatusChanged += OnToolStatusChanged;
            _testOrchestrator.LogReceived += OnLogReceived;

            InitializeAsync();
        }

        public bool CanStartTest => SelectedDriveCount > 0 && !IsTestRunning && HasSelectedTools;

        public bool HasSelectedTools =>
            GuiTools.Any(t => t.IsSelected) ||
            CliTools.Any(t => t.IsSelected) ||
            HybridTools.Any(t => t.IsSelected);

        public bool CanSelectAll => AvailableDrives.Any(d => !d.IsSelected);
        public bool CanDeselectAll => AvailableDrives.Any(d => d.IsSelected);

        public string TestSummary
        {
            get
            {
                var selectedToolCount = GuiTools.Count(t => t.IsSelected) +
                                      CliTools.Count(t => t.IsSelected) +
                                      HybridTools.Count(t => t.IsSelected);

                // return $"• 选中设备: {SelectedDriveCount} 个\n" +
                //        $"• 选中工具: {selectedToolCount} 个\n" +
                //        $"• 预计时间: 约 {EstimateTestTime()} 小时\n" +
                //        $"• 测试模式: 并行测试";
                string formatTemplate = Strings.TestSummaryFormat;
                return string.Format(formatTemplate,
                    SelectedDriveCount,
                    selectedToolCount,
                    EstimateTestTime());
            }
        }

        private async void InitializeAsync()
        {
            try
            {
                AddLogEntry(LogLevel.Info, "正在初始化应用程序...");

                // 异步初始化，但不使用Task.Run，因为我们需要在UI线程上更新集合
                await RefreshDrivesAsync();
                await InitializeTestToolsAsync();

                AddLogEntry(LogLevel.Info, "应用程序初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化失败");
                AddLogEntry(LogLevel.Error, $"初始化失败: {ex.Message}");
            }
        }

        private async Task RefreshDrivesAsync()
        {
            try
            {
                AddLogEntry(LogLevel.Info, Strings.Log_RefreshDrivers);

                // 使用Core服务获取可移动驱动器信息
                var coreDrives = await _systemInfoService.GetRemovableDrivesAsync();

                // 清空现有驱动器
                foreach (var drive in AvailableDrives)
                {
                    drive.PropertyChanged -= Drive_PropertyChanged;
                }
                AvailableDrives.Clear();

                // 转换Core驱动器信息到UI模型
                foreach (var coreDrive in coreDrives)
                {
                    var uiDrive = new UIDriveInfo
                    {
                        Name = coreDrive.Name,
                        Label = string.IsNullOrEmpty(coreDrive.Label) ? "" : coreDrive.Label,
                        TotalSize = coreDrive.TotalSize,
                        AvailableSpace = coreDrive.AvailableFreeSpace,
                        IsReady = true,
                        Status = Strings.Status_Ready
                    };

                    uiDrive.PropertyChanged += Drive_PropertyChanged;
                    AvailableDrives.Add(uiDrive);
                }

                UpdateSelectedDriveCount();

                AddLogEntry(LogLevel.Info, string.Format(Strings.Log_FoundRemovableDevice, AvailableDrives.Count));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新驱动器列表时出错");
                AddLogEntry(LogLevel.Error, string.Format(Strings.Error_RefreshDrives, ex.Message));
            }
        }

        private async Task InitializeTestToolsAsync()
        {
            try
            {
                AddLogEntry(LogLevel.Info, "正在发现测试工具...");

                var availableTools = await _testOrchestrator.GetAvailableToolsAsync();

                GuiTools.Clear();
                CliTools.Clear();
                HybridTools.Clear();

                foreach (var tool in availableTools)
                {
                    var toolViewModel = new TestToolViewModel
                    {
                        Name = tool.ToolName,
                        DisplayName = tool.ToolName,
                        IsSelected = false,
                        Status = tool.IsToolAvailable() ? TestToolStatus.Available : TestToolStatus.NotAvailable
                    };

                    switch (tool.ToolType)
                    {
                        case Core.Enums.TestToolType.GUI:
                            GuiTools.Add(toolViewModel);
                            break;
                        case Core.Enums.TestToolType.CLI:
                            CliTools.Add(toolViewModel);
                            break;
                        case Core.Enums.TestToolType.Hybrid:
                            HybridTools.Add(toolViewModel);
                            break;
                    }

                    toolViewModel.PropertyChanged += Tool_PropertyChanged;
                }

                AddLogEntry(LogLevel.Info, $"发现 {availableTools.Count} 个测试工具");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化测试工具失败");
                AddLogEntry(LogLevel.Error, $"初始化测试工具失败: {ex.Message}");
            }
        }

        private void OnTestSuiteStatusChanged(object? sender, TestSuiteStatusChangedEventArgs e)
        {
            Application.Current.Dispatcher.InvokeAsync(() =>
            {
                CurrentStatus = e.NewStatus.ToString();
                AddLogEntry(LogLevel.Info, $"测试套件状态变更: {e.NewStatus}");
            });
        }

        private void OnToolStatusChanged(object? sender, TestStatusChangedEventArgs e)
        {
            Application.Current.Dispatcher.InvokeAsync(() =>
            {
                CurrentTestName = e.ToolName;
                AddLogEntry(LogLevel.Info, $"工具 {e.ToolName} 状态变更: {e.NewStatus}");
            });
        }

        private void OnLogReceived(object? sender, LogEventArgs e)
        {
            Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var logLevel = e.Level switch
                {
                    Microsoft.Extensions.Logging.LogLevel.Error => LogLevel.Error,
                    Microsoft.Extensions.Logging.LogLevel.Warning => LogLevel.Warning,
                    Microsoft.Extensions.Logging.LogLevel.Information => LogLevel.Info,
                    _ => LogLevel.Debug
                };
                AddLogEntry(logLevel, e.Message);
            });
        }

        [RelayCommand]
        private async Task RefreshDrives()
        {
            await RefreshDrivesAsync();
        }

        [RelayCommand]
        private async Task StartTestAsync()
        {
            if (!CanStartTest) return;

            try
            {
                IsTestRunning = true;
                CurrentStatus = Strings.Status_PreparingTest;
                AddLogEntry(LogLevel.Info, Strings.Log_BeginTestExecution);

                // 创建取消令牌
                _testCancellationTokenSource = new CancellationTokenSource();

                // 构建测试配置
                var testConfiguration = BuildTestConfiguration();

                // 创建进度报告器
                var progress = new Progress<ProgressEventArgs>(args =>
                {
                    Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        OverallProgress = args.Progress;
                        CurrentStatus = args.Status;
                    });
                });

                // 执行真实的测试
                var testResult = await _testOrchestrator.ExecuteTestSuiteAsync(
                    testConfiguration,
                    _testCancellationTokenSource.Token,
                    progress);

                // 处理测试结果
                if (testResult.AllTestsPassed)
                {
                    AddLogEntry(LogLevel.Info, "所有测试执行成功");
                    CurrentStatus = Strings.Status_TestCompleted;
                }
                else
                {
                    AddLogEntry(LogLevel.Warning, $"测试执行完成，但有 {testResult.TestResults.Count(r => !r.Success)} 个测试失败");
                    CurrentStatus = "测试完成（有失败）";
                }

                // 生成报告
                AddLogEntry(LogLevel.Info, "正在生成测试报告...");
                // 这里可以调用报告服务生成报告
            }
            catch (OperationCanceledException)
            {
                AddLogEntry(LogLevel.Info, "测试已被用户取消");
                CurrentStatus = Strings.Status_TestStopped;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试执行失败");
                AddLogEntry(LogLevel.Error, string.Format(Strings.Log_TestExecutionFailed, ex.Message));
                CurrentStatus = "测试失败";
            }
            finally
            {
                IsTestRunning = false;
                _testCancellationTokenSource?.Dispose();
                _testCancellationTokenSource = null;
            }
        }

        [RelayCommand]
        private async Task StopTestAsync()
        {
            if (!IsTestRunning) return;

            try
            {
                AddLogEntry(LogLevel.Info, "正在停止测试...");

                // 取消当前测试
                _testCancellationTokenSource?.Cancel();

                // 通知测试编排器停止
                await _testOrchestrator.StopCurrentTestSuiteAsync();

                CurrentStatus = Strings.Status_TestStopped;
                AddLogEntry(LogLevel.Warning, Strings.Log_UserStoppedTest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止测试时出错");
                AddLogEntry(LogLevel.Error, $"停止测试时出错: {ex.Message}");
            }
        }

        private TestConfiguration BuildTestConfiguration()
        {
            var selectedTools = GuiTools.Concat(CliTools).Concat(HybridTools)
                .Where(t => t.IsSelected)
                .Select(t => t.Name)
                .ToList();

            var selectedDrives = AvailableDrives.Where(d => d.IsSelected).ToList();

            // 使用第一个选中的驱动器作为目标驱动器
            var targetDrive = selectedDrives.FirstOrDefault()?.Name ?? "";

            return new TestConfiguration
            {
                TargetDrive = targetDrive,
                SelectedTools = selectedTools,
                Parameters = new Dictionary<string, object>
                {
                    ["TestAllSpace"] = true,
                    ["VerifyData"] = true,
                    ["TestSize"] = 1024 // 1GB
                },
                TimeoutSeconds = 7200, // 2小时
                TestAllSpace = true,
                VerifyData = true,
                TestSize = "1GB"
            };
        }

        [RelayCommand]
        private void OpenSettings()
        {
            try
            {
                var settingsWindow = new Views.SettingsWindow();
                var result = settingsWindow.ShowDialog();

                if (result == true)
                {
                    AddLogEntry(LogLevel.Info, Strings.Log_SettingSaved);
                }
                else
                {
                    AddLogEntry(LogLevel.Info, Strings.Log_SettingCancelled);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开设置窗口失败");
                AddLogEntry(LogLevel.Error, string.Format(Strings.Log_OpenSettingWindowsFail, ex.Message));
            }
        }

        [RelayCommand]
        private void ClearLog()
        {
            LogEntries.Clear();
            AddLogEntry(LogLevel.Info, Strings.Log_ClearLog);
        }

        [RelayCommand]
        private void SaveLog()
        {
            // TODO: 实现保存日志功能
            AddLogEntry(LogLevel.Info, Strings.Log_SaveLogToFile);
        }

        [RelayCommand]
        private void SelectAllDrives()
        {
            foreach (var drive in AvailableDrives)
            {
                drive.IsSelected = true;
                drive.TestStatus = DriveTestStatus.Selected;
            }
            UpdateSelectedDriveCount();
            AddLogEntry(LogLevel.Info, string.Format(Strings.Log_SelectedDrives, AvailableDrives.Count));
        }

        [RelayCommand]
        private void DeselectAllDrives()
        {
            foreach (var drive in AvailableDrives)
            {
                drive.IsSelected = false;
                drive.TestStatus = DriveTestStatus.Ready;
            }
            UpdateSelectedDriveCount();
            AddLogEntry(LogLevel.Info, Strings.Log_AllDrivesDeselected);
        }

        [RelayCommand]
        private void ToggleDriveSelection(UIDriveInfo drive)
        {
            if (drive != null)
            {
                drive.IsSelected = !drive.IsSelected;
                drive.TestStatus = drive.IsSelected ? DriveTestStatus.Selected : DriveTestStatus.Ready;
                UpdateSelectedDriveCount();
                AddLogEntry(LogLevel.Info, drive.IsSelected
                    ? string.Format(Strings.Log_DriverSelected, drive.Name)
                    : string.Format(Strings.Log_DriverDeselected, drive.Name));
            }
        }







        private void AddLogEntry(LogLevel level, string message)
        {
            var entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                Source = "MainViewModel"
            };

            // 确保在UI线程上执行
            if (Application.Current.Dispatcher.CheckAccess())
            {
                // 已经在UI线程上
                LogEntries.Add(entry);

                // 限制日志条目数量
                while (LogEntries.Count > 1000)
                {
                    LogEntries.RemoveAt(0);
                }
            }
            else
            {
                // 不在UI线程上，需要调度到UI线程
                Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    LogEntries.Add(entry);

                    // 限制日志条目数量
                    while (LogEntries.Count > 1000)
                    {
                        LogEntries.RemoveAt(0);
                    }
                });
            }
        }

        private string EstimateTestTime()
        {
            var selectedToolCount = GuiTools.Count(t => t.IsSelected) +
                                  CliTools.Count(t => t.IsSelected) +
                                  HybridTools.Count(t => t.IsSelected);

            // 并行测试，时间不会因为设备数量增加而线性增长
            return (selectedToolCount * 0.5).ToString("F1");
        }

        private void UpdateSelectedDriveCount()
        {
            SelectedDriveCount = AvailableDrives.Count(d => d.IsSelected);
            OnPropertyChanged(nameof(CanStartTest));
            OnPropertyChanged(nameof(TestSummary));
            OnPropertyChanged(nameof(CanSelectAll));
            OnPropertyChanged(nameof(CanDeselectAll));
        }

        private void Drive_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(UIDriveInfo.IsSelected) && sender is UIDriveInfo drive)
            {
                UpdateSelectedDriveCount();
                AddLogEntry(LogLevel.Info, drive.IsSelected
                    ? string.Format(Strings.Log_DriverSelected, drive.Name)
                    : string.Format(Strings.Log_DriverDeselected, drive.Name));
            }
        }

        private void Tool_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TestToolViewModel.IsSelected))
            {
                OnPropertyChanged(nameof(CanStartTest));
                OnPropertyChanged(nameof(HasSelectedTools));
                OnPropertyChanged(nameof(TestSummary));
            }
        }

        partial void OnIsTestRunningChanged(bool value)
        {
            OnPropertyChanged(nameof(CanStartTest));
        }
    }
}

